import SwiftUI
import UIKit
import lingxia
import os.log

public struct ContentView: View {
    // Use a global flag instead of @State to avoid SwiftUI update cycle issues
    private static var hasInitialized = false

    public var body: some View {
        Color.clear
            .onAppear {
                if !Self.hasInitialized {
                    Self.hasInitialized = true
                    LxApp.initialize()

                    // Install test lxapp for openMiniApp testing
                    installTestMiniApp()

                    LxApp.openHomeLxApp()
                }
            }
    }
}

/// Install test lxapp for openMiniApp functionality testing
/// This copies the installed homelxapp from data directory to create a test lxapp
private func installTestMiniApp() {
    let log = OSLog(subsystem: "LingXia.MiniApp", category: "TestInstall")

    os_log("Installing test lxapp: 95dc2dcfcccc191", log: log, type: .info)

    // Get the documents directory path
    guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
        os_log("Failed to get documents directory", log: log, type: .error)
        return
    }

    // The actual data directory used by Rust has the bundle identifier suffix
    let bundleId = Bundle.main.bundleIdentifier ?? "unknown"
    let dataDir = documentsPath.appendingPathComponent("\(bundleId)")

    // Construct paths for source and destination
    let lingxiaDir = dataDir.appendingPathComponent("lingxia")
    let lxappsDir = lingxiaDir.appendingPathComponent("lxapps")
    let versionsDir = lingxiaDir.appendingPathComponent("versions")

    os_log("Looking for homelxapp in data directory: %@", log: log, type: .info, dataDir.path)

    let sourceDir = lxappsDir.appendingPathComponent("homelxapp")

    // Use the exact hash from the error message for the test lxapp
    let testAppId = "testlxapp"
    let hashedDirName = "95dc2dcfcccc191" // Direct hash from error message

    let destDir = lxappsDir.appendingPathComponent(hashedDirName)
    let sourceVersionFile = versionsDir.appendingPathComponent("homelxapp.txt")
    let destVersionFile = versionsDir.appendingPathComponent("\(testAppId).txt") // Version file uses original appId

    let fileManager = FileManager.default

    os_log("Using hash directory: %@ for appId: %@", log: log, type: .info, hashedDirName, testAppId)

    do {
        // Check if source homelxapp exists, if not try to copy from bundle
        if !fileManager.fileExists(atPath: sourceDir.path) {
            os_log("Source homelxapp not found at: %@", log: log, type: .error, sourceDir.path)

            // Try alternative path - check if homelxapp is in the bundle resources
            if let bundlePath = Bundle.main.path(forResource: "homelxapp", ofType: nil) {
                os_log("Found homelxapp in bundle at: %@", log: log, type: .info, bundlePath)
                // Copy from bundle to the expected location first
                let bundleSourceURL = URL(fileURLWithPath: bundlePath)
                try fileManager.copyItem(at: bundleSourceURL, to: sourceDir)
                os_log("Copied homelxapp from bundle to data directory", log: log, type: .info)
            } else {
                os_log("homelxapp not found in bundle either, cannot proceed", log: log, type: .error)
                return
            }
        }

        // Remove destination if it already exists
        if fileManager.fileExists(atPath: destDir.path) {
            try fileManager.removeItem(at: destDir)
            os_log("Removed existing test lxapp directory: %@", log: log, type: .info, destDir.path)
        }

        // Create destination directory first
        try fileManager.createDirectory(at: destDir, withIntermediateDirectories: true, attributes: nil)

        // Create destination directory first
        try fileManager.createDirectory(at: destDir, withIntermediateDirectories: true, attributes: nil)

        // Copy the entire homelxapp directory to create test lxapp using recursive copy
        do {
            try copyDirectoryContents(from: sourceDir, to: destDir, fileManager: fileManager)
            os_log("Successfully copied homelxapp to test lxapp directory: %@", log: log, type: .info, destDir.path)
        } catch {
            os_log("Failed to copy homelxapp directory: %@", log: log, type: .error, error.localizedDescription)

            // Try to list contents of source directory for debugging
            do {
                let contents = try fileManager.contentsOfDirectory(atPath: sourceDir.path)
                os_log("Source directory contents: %@", log: log, type: .info, contents.joined(separator: ", "))
            } catch {
                os_log("Failed to list source directory contents: %@", log: log, type: .error, error.localizedDescription)
            }
            throw error
        }

        // Copy version file if it exists
        if fileManager.fileExists(atPath: sourceVersionFile.path) {
            if fileManager.fileExists(atPath: destVersionFile.path) {
                try fileManager.removeItem(at: destVersionFile)
            }
            try fileManager.copyItem(at: sourceVersionFile, to: destVersionFile)
            os_log("Successfully copied version file", log: log, type: .info)
        }

        os_log("Test lxapp %@ installation completed successfully in directory: %@", log: log, type: .info, testAppId, hashedDirName)

    } catch {
        os_log("Failed to install test lxapp: %@", log: log, type: .error, error.localizedDescription)
    }
}

/// Recursively copy directory contents from source to destination
/// This is more reliable than copyItem for iOS sandbox environments
private func copyDirectoryContents(from sourceDir: URL, to destDir: URL, fileManager: FileManager) throws {
    let contents = try fileManager.contentsOfDirectory(at: sourceDir, includingPropertiesForKeys: nil, options: [])

    for item in contents {
        let destItem = destDir.appendingPathComponent(item.lastPathComponent)

        var isDirectory: ObjCBool = false
        if fileManager.fileExists(atPath: item.path, isDirectory: &isDirectory) {
            if isDirectory.boolValue {
                // Create subdirectory and recursively copy its contents
                try fileManager.createDirectory(at: destItem, withIntermediateDirectories: true, attributes: nil)
                try copyDirectoryContents(from: item, to: destItem, fileManager: fileManager)
            } else {
                // Copy file
                try fileManager.copyItem(at: item, to: destItem)
            }
        }
    }
}

@main
public struct LxAppApp: App {
    public init() { }

    public var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
